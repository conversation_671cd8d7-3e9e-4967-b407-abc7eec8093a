package message_biz

import (
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"gorm.io/gorm"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"sort"
	"time"
)

// MessageVO 消息视图对象
type MessageVO struct {
	ID        uint                    `json:"id"`
	Type      constmap.MessageType    `json:"type"`
	SubType   constmap.MessageSubType `json:"sub_type"`
	Title     string                  `json:"title"`
	Content   string                  `json:"content"`
	IsRead    int                     `json:"is_read"`
	ReadTime  int64                   `json:"read_time"`  // unix时间戳，0表示未读
	CreatedAt int64                   `json:"created_at"` // unix时间戳

	// 业务字段
	WishCommentId uint `json:"wish_comment_id,omitempty"`
	WishMemberId  uint `json:"wish_member_id,omitempty"`
}

// convertToMessageVO 通用VO转换函数
func convertToMessageVO(tpl *models.MessageTpl, isRead int, readTime *time.Time) *MessageVO {
	vo := &MessageVO{
		ID:            tpl.ID,
		Type:          tpl.Type,
		SubType:       tpl.SubType,
		Title:         tpl.Title,
		Content:       tpl.Content,
		IsRead:        isRead,
		CreatedAt:     tpl.CreatedAt.Unix(),
		WishCommentId: tpl.WishCommentId,
		WishMemberId:  tpl.WishMemberId,
	}
	if readTime != nil {
		vo.ReadTime = readTime.Unix()
	}
	return vo
}

// MessageTypeSummary 消息类型汇总
type MessageTypeSummary struct {
	Total       int64        `json:"total"`
	UnreadCount int64        `json:"unread_count"`
	Messages    []*MessageVO `json:"messages"`
}

// MessageBadge 消息徽章
type MessageBadge struct {
	SystemUnread  int64 `json:"system_unread"`
	TeamUnread    int64 `json:"team_unread"`
	CommentUnread int64 `json:"comment_unread"`
	TotalUnread   int64 `json:"total_unread"`
}

// 获取全员消息（scope=1）
func GetGlobalMessages(db *gorm.DB, userId uint, msgType constmap.MessageType, limit int) ([]*MessageVO, error) {
	var templates []*models.MessageTpl
	err := db.Where("type = ? AND scope = 1", msgType).
		Order("created_at DESC").
		Limit(limit).
		Find(&templates).Error
	if err != nil {
		return nil, utils.NewError(err)
	}

	if len(templates) == 0 {
		return []*MessageVO{}, nil
	}

	statusMap, err := GetUserMessageStatusMap(db, userId, extractTemplateIds(templates))
	if err != nil {
		return nil, err
	}

	var messages []*MessageVO
	for _, tpl := range templates {
		isRead := constmap.Disable
		var readTime *time.Time
		if status, exists := statusMap[tpl.ID]; exists {
			isRead = status.IsRead
			readTime = status.ReadTime
		}
		messages = append(messages, convertToMessageVO(tpl, isRead, readTime))
	}
	return messages, nil
}

// 获取个人消息（scope=2）
func GetPersonalMessages(db *gorm.DB, userId uint, msgType constmap.MessageType, limit int) ([]*MessageVO, error) {
	var results []struct {
		models.MessageTpl
		IsRead   int        `gorm:"column:is_read"`
		ReadTime *time.Time `gorm:"column:read_time"`
	}

	err := db.Model(&models.MessageTpl{}).
		Select("message_tpl.*, message.is_read, message.read_time").
		Joins("INNER JOIN message ON message_tpl.id = message.message_tpl_id").
		Where("message_tpl.type = ? AND message_tpl.scope = 2 AND message.user_id = ?", msgType, userId).
		Order("message_tpl.created_at DESC").
		Limit(limit).
		Find(&results).Error
	if err != nil {
		return nil, utils.NewError(err)
	}

	var messages []*MessageVO
	for _, result := range results {
		messages = append(messages, convertToMessageVO(&result.MessageTpl, result.IsRead, result.ReadTime))
	}
	return messages, nil
}

// 获取用户消息状态映射（带缓存）
func GetUserMessageStatusMap(db *gorm.DB, userId uint, templateIds []uint) (map[uint]*models.Message, error) {
	if len(templateIds) == 0 {
		return make(map[uint]*models.Message), nil
	}

	cacheKey := fmt.Sprintf(constmap.RKUserMsgStatus, userId)
	cdata := make(map[uint]*models.Message)
	cachedStatusMap := &utils.Marshaller[map[uint]*models.Message]{&cdata}

	if my_cache.Get(cacheKey, &cachedStatusMap) {
		filteredMap := make(map[uint]*models.Message)
		for _, id := range templateIds {
			if status, exists := (*cachedStatusMap.Data)[id]; exists {
				filteredMap[id] = status
			}
		}
		return filteredMap, nil
	}

	var messages []*models.Message
	err := db.Where("user_id = ? AND message_tpl_id IN ?", userId, templateIds).Find(&messages).Error
	if err != nil {
		return nil, utils.NewError(err)
	}

	for _, msg := range messages {
		cdata[msg.MessageTplId] = msg
	}

	_ = my_cache.Set(cacheKey, cachedStatusMap, constmap.TimeDur10m)
	return cdata, nil
}

// MergeMessages 合并消息列表
func MergeMessages(globalMessages, personalMessages []*MessageVO, limit int) []*MessageVO {
	allMessages := append(globalMessages, personalMessages...)

	// 按创建时间倒序排序
	sort.Slice(allMessages, func(i, j int) bool {
		return allMessages[i].CreatedAt > allMessages[j].CreatedAt
	})

	if len(allMessages) > limit {
		allMessages = allMessages[:limit]
	}
	return allMessages
}

// MessageTypeStats 消息类型统计数据
type MessageTypeStats struct {
	Total       int64 `json:"total"`
	UnreadCount int64 `json:"unread_count"`
}

// 获取消息统计数量（优化版：分离查询 + 缓存）
func GetMessageCounts(db *gorm.DB, userId uint, msgType constmap.MessageType) (total, unreadCount int64, err error) {
	// 尝试从缓存获取统计数据
	cacheKey := fmt.Sprintf(constmap.RKMsgTypeStats, userId, msgType)
	var stats *MessageTypeStats

	if my_cache.Get(cacheKey, &stats) {
		return stats.Total, stats.UnreadCount, nil
	}

	// 缓存未命中，分离查询避免复杂JOIN
	// 1. 统计全员消息
	var globalTotal int64
	err = db.Model(&models.MessageTpl{}).
		Where("type = ? AND scope = 1", msgType).
		Count(&globalTotal).Error
	if err != nil {
		return 0, 0, utils.NewError(err)
	}

	// 2. 统计全员消息已读数
	var globalRead int64
	err = db.Model(&models.Message{}).
		Joins("INNER JOIN message_tpl ON message_tpl.id = message.message_tpl_id").
		Where("message_tpl.type = ? AND message_tpl.scope = 1 AND message.user_id = ? AND message.is_read = 1", msgType, userId).
		Count(&globalRead).Error
	if err != nil {
		return 0, 0, utils.NewError(err)
	}

	// 3. 统计个人消息（总数和未读数一起查）
	var personalTotal, personalUnread int64
	err = db.Model(&models.Message{}).
		Select("COUNT(*) as total, COUNT(CASE WHEN is_read = 2 THEN 1 END) as unread").
		Joins("INNER JOIN message_tpl ON message_tpl.id = message.message_tpl_id").
		Where("message_tpl.type = ? AND message_tpl.scope = 2 AND message.user_id = ?", msgType, userId).
		Row().Scan(&personalTotal, &personalUnread)
	if err != nil {
		return 0, 0, utils.NewError(err)
	}

	globalUnread := globalTotal - globalRead
	total = globalTotal + personalTotal
	unreadCount = globalUnread + personalUnread

	// 缓存统计结果5分钟
	stats = &MessageTypeStats{
		Total:       total,
		UnreadCount: unreadCount,
	}
	_ = my_cache.Set(cacheKey, stats, 5*time.Minute)

	return total, unreadCount, nil
}

// 获取消息徽章（未读数统计）优化版：批量查询
func GetMessageBadge(db *gorm.DB, userId uint) (*MessageBadge, error) {
	// 尝试从缓存获取
	cacheKey := fmt.Sprintf(constmap.RKMsgBadge, userId)
	var badge = &utils.Marshaller[MessageBadge]{new(MessageBadge)}

	if my_cache.Get(cacheKey, &badge) {
		return badge.Data, nil
	}

	// 缓存未命中，使用ORM格式批量查询所有类型的统计数据
	var results []struct {
		Type           int   `gorm:"column:type"`
		GlobalTotal    int64 `gorm:"column:global_total"`
		GlobalRead     int64 `gorm:"column:global_read"`
		PersonalTotal  int64 `gorm:"column:personal_total"`
		PersonalUnread int64 `gorm:"column:personal_unread"`
	}

	err := db.Model(&models.MessageTpl{}).
		Select(`
			message_tpl.type,
			COUNT(CASE WHEN message_tpl.scope = 1 THEN 1 END) as global_total,
			COUNT(CASE WHEN message_tpl.scope = 1 AND message.is_read = 1 THEN 1 END) as global_read,
			COUNT(CASE WHEN message_tpl.scope = 2 THEN 1 END) as personal_total,
			COUNT(CASE WHEN message_tpl.scope = 2 AND message.is_read = 2 THEN 1 END) as personal_unread
		`).
		Joins("LEFT JOIN message ON message_tpl.id = message.message_tpl_id AND message.user_id = ?", userId).
		Where("message_tpl.type IN ?", []constmap.MessageType{
			constmap.MessageTypeSystem,
			constmap.MessageTypeTeam,
			constmap.MessageTypeComment,
		}).
		Group("message_tpl.type").
		Find(&results).Error

	if err != nil {
		return nil, utils.NewError(err)
	}

	badge.Data = &MessageBadge{}
	badgeData := badge.Data

	for _, result := range results {
		unreadCount := (result.GlobalTotal - result.GlobalRead) + result.PersonalUnread

		switch constmap.MessageType(result.Type) {
		case constmap.MessageTypeSystem:
			badgeData.SystemUnread = unreadCount
		case constmap.MessageTypeTeam:
			badgeData.TeamUnread = unreadCount
		case constmap.MessageTypeComment:
			badgeData.CommentUnread = unreadCount
		}
	}

	badgeData.TotalUnread = badgeData.SystemUnread + badgeData.TeamUnread + badgeData.CommentUnread

	_ = my_cache.Set(cacheKey, badge, constmap.TimeDur10m)

	return badge.Data, nil
}

// 批量设置消息已读（仅普通文本消息）
func BatchSetMessageRead(db *gorm.DB, userId uint, messageIds []uint) error {
	if len(messageIds) == 0 {
		return nil
	}

	// 查询符合条件的消息模板（仅普通文本消息）
	var validTemplates []*models.MessageTpl
	err := db.Where("id IN ? AND sub_type = ?", messageIds, constmap.MessageSubTypeText).
		Find(&validTemplates).Error
	if err != nil {
		return utils.NewError(err)
	}

	if len(validTemplates) == 0 {
		return nil
	}

	validIds := make([]uint, len(validTemplates))
	for i, tpl := range validTemplates {
		validIds[i] = tpl.ID
	}

	// 批量创建或更新消息状态
	now := time.Now()
	var messages []*models.Message

	for _, templateId := range validIds {
		messages = append(messages, &models.Message{
			MessageTplId: templateId,
			UserId:       userId,
			IsRead:       constmap.Enable,
			ReadTime:     &now,
		})
	}

	// 使用事务批量插入或更新
	err = db.Transaction(func(tx *gorm.DB) error {
		for _, msg := range messages {
			err := tx.Where(models.Message{MessageTplId: msg.MessageTplId, UserId: msg.UserId}).
				Assign(models.Message{IsRead: constmap.Enable, ReadTime: &now}).
				FirstOrCreate(msg).Error
			if err != nil {
				return err
			}
		}
		return nil
	})

	if err != nil {
		return utils.NewError(err)
	}

	// 清除相关缓存
	ClearUserMessageCache(userId)

	return nil
}

// GetMessageTypeSummaries 批量获取指定类型消息汇总（按类型分别缓存）
func GetMessageTypeSummaries(db *gorm.DB, userId uint, messageTypes []constmap.MessageType, messageLimit int) (map[constmap.MessageType]*MessageTypeSummary, error) {
	summaryMap := make(map[constmap.MessageType]*MessageTypeSummary)

	for _, msgType := range messageTypes {
		cacheKey := fmt.Sprintf(constmap.RKMsgTypeSummary, userId, msgType)
		summary := &utils.Marshaller[MessageTypeSummary]{new(MessageTypeSummary)}

		if my_cache.Get(cacheKey, &summary) {
			summaryMap[msgType] = summary.Data
			continue
		}

		// 缓存未命中，查询数据
		globalMessages, err := GetGlobalMessages(db, userId, msgType, messageLimit)
		if err != nil {
			return nil, err
		}

		personalMessages, err := GetPersonalMessages(db, userId, msgType, messageLimit)
		if err != nil {
			return nil, err
		}

		allMessages := MergeMessages(globalMessages, personalMessages, messageLimit)

		total, unreadCount, err := GetMessageCounts(db, userId, msgType)
		if err != nil {
			return nil, err
		}

		summary.Data = &MessageTypeSummary{
			Total:       total,
			UnreadCount: unreadCount,
			Messages:    allMessages,
		}

		summaryMap[msgType] = summary.Data
		_ = my_cache.Set(cacheKey, summary, constmap.TimeDur10m)
	}

	return summaryMap, nil
}

// 清除用户消息相关缓存（优化版：批量删除）
func ClearUserMessageCache(userId uint) {
	// 收集所有需要删除的缓存key
	var keys []string

	// 用户消息状态缓存
	keys = append(keys, fmt.Sprintf(constmap.RKUserMsgStatus, userId))

	// 消息徽章缓存
	keys = append(keys, fmt.Sprintf(constmap.RKMsgBadge, userId))

	// 各类型消息相关缓存
	for _, msgType := range []constmap.MessageType{
		constmap.MessageTypeSystem,
		constmap.MessageTypeTeam,
		constmap.MessageTypeComment,
	} {
		// 消息汇总缓存
		keys = append(keys, fmt.Sprintf(constmap.RKMsgTypeSummary, userId, msgType))
		// 消息统计缓存
		keys = append(keys, fmt.Sprintf(constmap.RKMsgTypeStats, userId, msgType))
	}

	// 使用pipeline批量删除
	my_cache.RedisClient().Del(keys...)
}

// 辅助函数：提取模板ID列表（使用slice.Map简化）
func extractTemplateIds(templates []*models.MessageTpl) []uint {
	return slice.Map(templates, func(_ int, tpl *models.MessageTpl) uint {
		return tpl.ID
	})
}
