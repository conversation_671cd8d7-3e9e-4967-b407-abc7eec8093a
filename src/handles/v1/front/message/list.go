package message

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/message_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"sort"
	"time"
)

// 消息列表响应
type MessageListResponse struct {
	HasMore bool                     `json:"has_more"`
	List    []*message_biz.MessageVO `json:"list"`
}

// 消息列表
func List(ctx *gin.Context) (any, error) {
	var in struct {
		Type   constmap.MessageType `form:"type" binding:"required"`
		IsRead int                  `form:"is_read"` // 可选：筛选已读状态 0=全部 1=已读 2=未读
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	session, _ := business.GetFrontLoginUser(ctx) // 路由中间件已验证登录

	db := utils.GetDB(ctx)
	page, pageSize := utils.GetPage(ctx)
	userId := session.UserId

	// 查询结果结构
	var results []struct {
		models.MessageTpl
		IsRead   int        `gorm:"column:is_read"`
		ReadTime *time.Time `gorm:"column:read_time"`
	}

	// 分离查询策略：分别查询全员消息和个人消息，避免复杂JOIN和EXISTS子查询
	var allResults []struct {
		models.MessageTpl
		IsRead   int        `gorm:"column:is_read"`
		ReadTime *time.Time `gorm:"column:read_time"`
	}

	// 1. 查询全员消息（scope=1）
	var globalResults []struct {
		models.MessageTpl
		IsRead   int        `gorm:"column:is_read"`
		ReadTime *time.Time `gorm:"column:read_time"`
	}

	globalQuery := db.Model(&models.MessageTpl{}).
		Select(`
			message_tpl.*,
			COALESCE(message.is_read, 2) as is_read,
			message.read_time
		`).
		Joins(`
			LEFT JOIN message ON message_tpl.id = message.message_tpl_id AND message.user_id = ?
		`, userId).
		Where("message_tpl.type = ? AND message_tpl.scope = 1", in.Type)

	// 添加全员消息的已读状态筛选
	if in.IsRead == constmap.Enable { // 已读
		globalQuery = globalQuery.Where("COALESCE(message.is_read, 2) = 1")
	} else if in.IsRead == constmap.Disable { // 未读
		globalQuery = globalQuery.Where("COALESCE(message.is_read, 2) = 2")
	}

	err := globalQuery.Find(&globalResults).Error
	if err != nil {
		return nil, utils.NewError(err)
	}

	// 2. 查询个人消息（scope=2）
	var personalResults []struct {
		models.MessageTpl
		IsRead   int        `gorm:"column:is_read"`
		ReadTime *time.Time `gorm:"column:read_time"`
	}

	personalQuery := db.Model(&models.MessageTpl{}).
		Select("message_tpl.*, message.is_read, message.read_time").
		Joins("INNER JOIN message ON message_tpl.id = message.message_tpl_id").
		Where("message_tpl.type = ? AND message_tpl.scope = 2 AND message.user_id = ?", in.Type, userId)

	// 添加个人消息的已读状态筛选
	if in.IsRead == constmap.Enable { // 已读
		personalQuery = personalQuery.Where("message.is_read = 1")
	} else if in.IsRead == constmap.Disable { // 未读
		personalQuery = personalQuery.Where("message.is_read = 2")
	}

	err = personalQuery.Find(&personalResults).Error
	if err != nil {
		return nil, utils.NewError(err)
	}

	// 3. 合并结果并按ID倒序排序
	allResults = append(allResults, globalResults...)
	allResults = append(allResults, personalResults...)

	// 按message_tpl.id倒序排序
	sort.Slice(allResults, func(i, j int) bool {
		return allResults[i].ID > allResults[j].ID
	})

	// 4. 应用分页和has_more判断
	totalResults := len(allResults)
	startIndex := (page - 1) * pageSize
	endIndex := startIndex + pageSize

	if startIndex >= totalResults {
		// 超出范围，返回空结果
		return MessageListResponse{
			HasMore: false,
			List:    []*message_biz.MessageVO{},
		}, nil
	}

	hasMore := endIndex < totalResults
	if endIndex > totalResults {
		endIndex = totalResults
	}

	results = allResults[startIndex:endIndex]

	// hasMore已在上面计算

	// 转换为MessageVO格式
	var messages []*message_biz.MessageVO
	for _, result := range results {
		vo := &message_biz.MessageVO{
			ID:            result.ID,
			Type:          result.Type,
			SubType:       result.SubType,
			Title:         result.Title,
			Content:       result.Content,
			IsRead:        result.IsRead,
			CreatedAt:     result.CreatedAt.Unix(),
			WishCommentId: result.WishCommentId,
			WishMemberId:  result.WishMemberId,
		}
		if result.ReadTime != nil {
			vo.ReadTime = result.ReadTime.Unix()
		}
		messages = append(messages, vo)
	}

	return MessageListResponse{
		HasMore: hasMore,
		List:    messages,
	}, nil
}
