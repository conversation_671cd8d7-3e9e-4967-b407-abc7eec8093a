package message

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/message_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

// 消息列表响应
type MessageListResponse struct {
	HasMore bool                     `json:"has_more"`
	List    []*message_biz.MessageVO `json:"list"`
}

// 消息列表
func List(ctx *gin.Context) (any, error) {
	var in struct {
		Type   constmap.MessageType `form:"type" binding:"required"`
		IsRead int                  `form:"is_read"` // 可选：筛选已读状态 0=全部 1=已读 2=未读
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	session, _ := business.GetFrontLoginUser(ctx) // 路由中间件已验证登录

	db := utils.GetDB(ctx)
	page, pageSize := utils.GetPage(ctx)
	userId := session.UserId

	// 查询结果结构
	var results []struct {
		models.MessageTpl
		IsRead   int        `gorm:"column:is_read"`
		ReadTime *time.Time `gorm:"column:read_time"`
	}

	// 构建基础查询 - 统一处理全员和个人消息
	query := db.Model(&models.MessageTpl{}).
		Select(`
			message_tpl.*,
			CASE
				WHEN message_tpl.scope = 1 THEN COALESCE(message.is_read, 2)
				ELSE message.is_read
			END as is_read,
			message.read_time
		`).
		Joins(`
			LEFT JOIN message ON message_tpl.id = message.message_tpl_id AND message.user_id = ?
		`, userId).
		Where("message_tpl.type = ? AND message_tpl.status = 1", in.Type).
		Where(`
			message_tpl.scope = 1 OR
			(message_tpl.scope = 2 AND EXISTS (
				SELECT 1 FROM message m2
				WHERE m2.message_tpl_id = message_tpl.id AND m2.user_id = ?
			))
		`, userId)

	// 添加已读状态筛选
	if in.IsRead == constmap.Enable { // 已读
		query = query.Where(`
			(message_tpl.scope = 1 AND COALESCE(message.is_read, 2) = 1) OR
			(message_tpl.scope = 2 AND message.is_read = 1)
		`)
	} else if in.IsRead == constmap.Disable { // 未读
		query = query.Where(`
			(message_tpl.scope = 1 AND COALESCE(message.is_read, 2) = 2) OR
			(message_tpl.scope = 2 AND message.is_read = 2)
		`)
	}

	// 查询pageSize+1条记录来判断has_more
	err := query.Order("message_tpl.id DESC").
		Limit(pageSize + 1).
		Offset((page - 1) * pageSize).
		Find(&results).Error
	if err != nil {
		return nil, utils.NewError(err)
	}

	// 判断是否有更多数据
	hasMore := len(results) > pageSize
	if hasMore {
		results = results[:pageSize] // 只返回pageSize条数据
	}

	// 转换为MessageVO格式
	var messages []*message_biz.MessageVO
	for _, result := range results {
		vo := &message_biz.MessageVO{
			ID:            result.ID,
			Type:          result.Type,
			SubType:       result.SubType,
			Title:         result.Title,
			Content:       result.Content,
			IsRead:        result.IsRead,
			CreatedAt:     result.CreatedAt.Unix(),
			WishCommentId: result.WishCommentId,
			WishMemberId:  result.WishMemberId,
		}
		if result.ReadTime != nil {
			vo.ReadTime = result.ReadTime.Unix()
		}
		messages = append(messages, vo)
	}

	return MessageListResponse{
		HasMore: hasMore,
		List:    messages,
	}, nil
}
