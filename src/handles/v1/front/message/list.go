package message

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/message_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

// 消息列表响应
type MessageListResponse struct {
	HasMore bool                     `json:"has_more"`
	List    []*message_biz.MessageVO `json:"list"`
}

// 消息列表
func List(ctx *gin.Context) (any, error) {
	var in struct {
		Type   constmap.MessageType `form:"type" binding:"required"`
		IsRead int                  `form:"is_read"` // 可选：筛选已读状态 0=全部 1=已读 2=未读
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	session, _ := business.GetFrontLoginUser(ctx) // 路由中间件已验证登录

	db := utils.GetDB(ctx)
	page, pageSize := utils.GetPage(ctx)
	userId := session.UserId

	// 查询结果结构
	var results []struct {
		models.MessageTpl
		IsRead   int        `gorm:"column:is_read"`
		ReadTime *time.Time `gorm:"column:read_time"`
	}

	// 使用UNION ALL在数据库层合并查询并分页，避免应用层处理大量数据

	// 构建已读状态筛选条件
	var readCondition string
	if in.IsRead == constmap.Enable { // 已读
		readCondition = " AND COALESCE(message.is_read, 2) = 1"
	} else if in.IsRead == constmap.Disable { // 未读
		readCondition = " AND COALESCE(message.is_read, 2) = 2"
	}

	// 构建个人消息的已读状态筛选条件
	var personalReadCondition string
	if in.IsRead == constmap.Enable { // 已读
		personalReadCondition = " AND message.is_read = 1"
	} else if in.IsRead == constmap.Disable { // 未读
		personalReadCondition = " AND message.is_read = 2"
	}

	// 构建UNION ALL查询：合并全员消息和个人消息
	unionSQL := fmt.Sprintf(`
		(
			SELECT message_tpl.*,
				   COALESCE(message.is_read, 2) as is_read,
				   message.read_time
			FROM message_tpl
			LEFT JOIN message ON message_tpl.id = message.message_tpl_id AND message.user_id = ?
			WHERE message_tpl.type = ? AND message_tpl.scope = 1%s
		)
		UNION ALL
		(
			SELECT message_tpl.*,
				   message.is_read,
				   message.read_time
			FROM message_tpl
			INNER JOIN message ON message_tpl.id = message.message_tpl_id
			WHERE message_tpl.type = ? AND message_tpl.scope = 2 AND message.user_id = ?%s
		)
		ORDER BY id DESC
		LIMIT ? OFFSET ?
	`, readCondition, personalReadCondition)

	// 准备查询参数
	var args []interface{}
	args = append(args, userId, in.Type, in.Type, userId)

	// 查询pageSize+1条记录来判断has_more
	args = append(args, pageSize+1, (page-1)*pageSize)

	// 执行查询
	err := db.Raw(unionSQL, args...).Find(&results).Error
	if err != nil {
		return nil, utils.NewError(err)
	}

	// 判断是否有更多数据
	hasMore := len(results) > pageSize
	if hasMore {
		results = results[:pageSize] // 只返回pageSize条数据
	}

	// 转换为MessageVO格式
	var messages []*message_biz.MessageVO
	for _, result := range results {
		vo := &message_biz.MessageVO{
			ID:            result.ID,
			Type:          result.Type,
			SubType:       result.SubType,
			Title:         result.Title,
			Content:       result.Content,
			IsRead:        result.IsRead,
			CreatedAt:     result.CreatedAt.Unix(),
			WishCommentId: result.WishCommentId,
			WishMemberId:  result.WishMemberId,
		}
		if result.ReadTime != nil {
			vo.ReadTime = result.ReadTime.Unix()
		}
		messages = append(messages, vo)
	}

	return MessageListResponse{
		HasMore: hasMore,
		List:    messages,
	}, nil
}
