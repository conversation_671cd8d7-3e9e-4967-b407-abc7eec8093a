package message

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/message_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"sort"
	"time"
)

// 消息列表响应
type MessageListResponse struct {
	HasMore bool                     `json:"has_more"`
	List    []*message_biz.MessageVO `json:"list"`
}

// 消息列表
func List(ctx *gin.Context) (any, error) {
	var in struct {
		Type   constmap.MessageType `form:"type" binding:"required"`
		IsRead int                  `form:"is_read"` // 可选：筛选已读状态 0=全部 1=已读 2=未读
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	session, _ := business.GetFrontLoginUser(ctx) // 路由中间件已验证登录

	db := utils.GetDB(ctx)
	page, pageSize := utils.GetPage(ctx)
	userId := session.UserId

	// 查询结果结构
	var results []struct {
		models.MessageTpl
		IsRead   int        `gorm:"column:is_read"`
		ReadTime *time.Time `gorm:"column:read_time"`
	}

	// 分别查询两种scope的消息，每种查pageSize+1条，然后合并排序
	var allResults []struct {
		models.MessageTpl
		IsRead   int        `gorm:"column:is_read"`
		ReadTime *time.Time `gorm:"column:read_time"`
	}

	// 1. 查询全员消息（scope=1），查pageSize+1条
	var globalResults []struct {
		models.MessageTpl
		IsRead   int        `gorm:"column:is_read"`
		ReadTime *time.Time `gorm:"column:read_time"`
	}

	globalQuery := db.Model(&models.MessageTpl{}).
		Select(`
			message_tpl.*,
			COALESCE(message.is_read, 2) as is_read,
			message.read_time
		`).
		Joins(`
			LEFT JOIN message ON message_tpl.id = message.message_tpl_id AND message.user_id = ?
		`, userId).
		Where("message_tpl.type = ? AND message_tpl.scope = 1", in.Type)

	// 添加全员消息的已读状态筛选
	if in.IsRead == constmap.Enable { // 已读
		globalQuery = globalQuery.Where("COALESCE(message.is_read, 2) = 1")
	} else if in.IsRead == constmap.Disable { // 未读
		globalQuery = globalQuery.Where("COALESCE(message.is_read, 2) = 2")
	}

	err := globalQuery.Order("message_tpl.id DESC").
		Limit(pageSize + 1).
		Find(&globalResults).Error
	if err != nil {
		return nil, utils.NewError(err)
	}

	// 2. 查询个人消息（scope=2），查pageSize+1条
	var personalResults []struct {
		models.MessageTpl
		IsRead   int        `gorm:"column:is_read"`
		ReadTime *time.Time `gorm:"column:read_time"`
	}

	personalQuery := db.Model(&models.MessageTpl{}).
		Select("message_tpl.*, message.is_read, message.read_time").
		Joins("INNER JOIN message ON message_tpl.id = message.message_tpl_id").
		Where("message_tpl.type = ? AND message_tpl.scope = 2 AND message.user_id = ?", in.Type, userId)

	// 添加个人消息的已读状态筛选
	if in.IsRead == constmap.Enable { // 已读
		personalQuery = personalQuery.Where("message.is_read = 1")
	} else if in.IsRead == constmap.Disable { // 未读
		personalQuery = personalQuery.Where("message.is_read = 2")
	}

	err = personalQuery.Order("message_tpl.id DESC").
		Limit(pageSize + 1).
		Find(&personalResults).Error
	if err != nil {
		return nil, utils.NewError(err)
	}

	// 3. 合并两种消息并按ID倒序排序
	allResults = append(allResults, globalResults...)
	allResults = append(allResults, personalResults...)

	// 按message_tpl.id倒序排序
	sort.Slice(allResults, func(i, j int) bool {
		return allResults[i].ID > allResults[j].ID
	})

	// 4. 应用分页逻辑
	// 注意：这里的分页逻辑是基于合并后的结果，不是真正的数据库分页
	// 但由于我们每种scope最多查pageSize+1条，所以适用于前几页的查询

	startIndex := (page - 1) * pageSize
	endIndex := startIndex + pageSize

	// 判断has_more：如果合并后的结果超过当前页需要的数据，说明可能有更多
	hasMore := len(allResults) > endIndex

	// 截取当前页的数据
	if startIndex >= len(allResults) {
		// 超出范围，返回空结果
		return MessageListResponse{
			HasMore: false,
			List:    []*message_biz.MessageVO{},
		}, nil
	}

	if endIndex > len(allResults) {
		endIndex = len(allResults)
	}

	results = allResults[startIndex:endIndex]

	// 转换为MessageVO格式
	var messages []*message_biz.MessageVO
	for _, result := range results {
		vo := &message_biz.MessageVO{
			ID:            result.ID,
			Type:          result.Type,
			SubType:       result.SubType,
			Title:         result.Title,
			Content:       result.Content,
			IsRead:        result.IsRead,
			CreatedAt:     result.CreatedAt.Unix(),
			WishCommentId: result.WishCommentId,
			WishMemberId:  result.WishMemberId,
		}
		if result.ReadTime != nil {
			vo.ReadTime = result.ReadTime.Unix()
		}
		messages = append(messages, vo)
	}

	return MessageListResponse{
		HasMore: hasMore,
		List:    messages,
	}, nil
}
